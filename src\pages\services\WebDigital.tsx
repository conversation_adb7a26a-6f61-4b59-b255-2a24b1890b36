
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Clock, DollarSign, TrendingUp, Users, Zap, Shield, Target, ArrowRight, Star } from "lucide-react";

const WebDigital = () => {
  const painPoints = [
    {
      icon: Clock,
      title: "Slow Website Killing Sales",
      description: "Your website takes 5+ seconds to load, causing 40% of visitors to leave before seeing your products."
    },
    {
      icon: DollarSign,
      title: "Losing to Competitors Online",
      description: "Competitors with better websites are capturing customers you should be winning."
    },
    {
      icon: Users,
      title: "Mobile Users Frustrated",
      description: "60% of your traffic is mobile, but your site doesn't work properly on phones and tablets."
    }
  ];

  const solutions = [
    {
      icon: Zap,
      title: "Lightning-Fast Performance",
      description: "Websites that load in under 1 second, keeping visitors engaged and converting.",
      metric: "70% faster load times"
    },
    {
      icon: Target,
      title: "Mobile-First Design",
      description: "Perfect experience on every device, capturing mobile customers effectively.",
      metric: "40% more mobile conversions"
    },
    {
      icon: TrendingUp,
      title: "SEO-Optimized Structure",
      description: "Built to rank higher in Google, bringing you more organic traffic.",
      metric: "3x more organic visitors"
    }
  ];

  const testimonials = [
    {
      quote: "Our new website increased online inquiries by 180% in just 3 months. The investment paid for itself within 6 weeks.",
      author: "Sarah Mitchell",
      company: "Coastal Accounting Services",
      size: "15 employees",
      result: "180% more inquiries"
    },
    {
      quote: "Finally, a website that actually works on mobile. Our mobile sales went from 20% to 65% of total revenue.",
      author: "Mike Chen",
      company: "Chen's Auto Repair",
      size: "8 employees",
      result: "65% mobile sales"
    },
    {
      quote: "They delivered exactly what they promised, on time and within budget. No surprises, just results.",
      author: "Lisa Rodriguez",
      company: "Rodriguez Legal Group",
      size: "12 employees",
      result: "On time & budget"
    }
  ];

  const processSteps = [
    {
      step: "1",
      title: "Free Strategy Call",
      description: "15-minute call to understand your business goals and current challenges."
    },
    {
      step: "2",
      title: "Custom Proposal",
      description: "Detailed plan with timeline, features, and transparent pricing within 48 hours."
    },
    {
      step: "3",
      title: "Launch & Grow",
      description: "Professional website delivered in 2-4 weeks with ongoing support included."
    }
  ];

  const features = [
    "Professional design that builds trust with customers",
    "Mobile-responsive for perfect phone/tablet experience",
    "SEO-optimized to rank higher in Google searches",
    "Fast loading speeds (under 1 second)",
    "Secure hosting with 99.9% uptime guarantee",
    "Easy content management system",
    "Contact forms that actually work",
    "Google Analytics integration for tracking results"
  ];

  const faqs = [
    {
      question: "How much does a professional website cost?",
      answer: "Our SMO-focused websites start at $2,500 for a professional 5-page site. We offer payment plans to fit your budget, and most clients see ROI within 3 months."
    },
    {
      question: "How long does it take to build a website?",
      answer: "Most websites are completed in 2-4 weeks. We work efficiently to get you online quickly without compromising quality."
    },
    {
      question: "Will I be able to update the website myself?",
      answer: "Yes! We build on user-friendly platforms and provide training so you can easily update content, add photos, and make changes."
    },
    {
      question: "What if I need changes after the website is live?",
      answer: "All our websites include 30 days of free revisions. After that, we offer affordable maintenance plans starting at $99/month."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="py-20 lg:py-28 pt-20 bg-white">
        <div className="max-w-content mx-auto px-6 text-center">
          <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm mb-6">
            <span className="text-sm font-medium text-lumen-charcoal">For Small & Medium Organizations</span>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold text-lumen-charcoal mb-6 leading-tight">
            Professional Websites That Actually <span className="text-lumen-yellow">Generate Leads</span>
          </h1>
          <p className="text-xl lg:text-2xl text-lumen-mid-gray mb-8 max-w-3xl mx-auto leading-relaxed">
            Stop losing customers to slow, outdated websites. Get a professional, mobile-friendly site that converts visitors into paying customers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button size="lg" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-10 py-6 text-lg">
              Get Your Free Website Audit
            </Button>
            <Button variant="outline" size="lg" className="border-2 border-lumen-charcoal/20 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg">
              Book Strategy Call
            </Button>
          </div>
          <p className="text-sm text-lumen-mid-gray">
            ✓ No obligation • ✓ 15-minute call • ✓ Actionable insights guaranteed
          </p>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-20 bg-lumen-off-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              Is Your Website Costing You Customers?
            </h2>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              Most small business websites are actually hurting sales. Here's what we see every day:
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {painPoints.map((point, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <point.icon className="h-8 w-8 text-red-600" />
                  </div>
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-4">{point.title}</h3>
                  <p className="text-lumen-mid-gray">{point.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-20 bg-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              How We Fix These Problems
            </h2>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              Professional websites designed specifically for small businesses that need results, not just pretty designs.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {solutions.map((solution, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-lumen-yellow/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <solution.icon className="h-8 w-8 text-lumen-yellow" />
                  </div>
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-4">{solution.title}</h3>
                  <p className="text-lumen-mid-gray mb-4">{solution.description}</p>
                  <div className="inline-block px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                    {solution.metric}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-20 bg-lumen-off-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              Real Results from Real SMO Clients
            </h2>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              See how other small businesses transformed their online presence and grew their revenue.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-8">
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-lumen-charcoal mb-6 italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="border-t pt-4">
                    <div className="font-bold text-lumen-charcoal">{testimonial.author}</div>
                    <div className="text-lumen-mid-gray text-sm">{testimonial.company} • {testimonial.size}</div>
                    <div className="inline-block mt-2 px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      {testimonial.result}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Lead Magnet Section */}
      <section className="py-20 bg-lumen-yellow">
        <div className="max-w-content mx-auto px-6 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
            Get Your Free Website Performance Audit
          </h2>
          <p className="text-xl text-lumen-charcoal/80 mb-8 max-w-3xl mx-auto">
            We'll analyze your current website and show you exactly what's costing you customers.
            15-minute call with actionable insights you can implement immediately.
          </p>
          <div className="grid md:grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-lumen-charcoal/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-lumen-charcoal" />
              </div>
              <h3 className="font-bold text-lumen-charcoal mb-2">Performance Analysis</h3>
              <p className="text-lumen-charcoal/70 text-sm">Speed, mobile-friendliness, and user experience review</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-lumen-charcoal/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-lumen-charcoal" />
              </div>
              <h3 className="font-bold text-lumen-charcoal mb-2">SEO Assessment</h3>
              <p className="text-lumen-charcoal/70 text-sm">How to rank higher and get more organic traffic</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-lumen-charcoal/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="h-8 w-8 text-lumen-charcoal" />
              </div>
              <h3 className="font-bold text-lumen-charcoal mb-2">Conversion Review</h3>
              <p className="text-lumen-charcoal/70 text-sm">Simple changes to turn more visitors into customers</p>
            </div>
          </div>
          <Button size="lg" className="bg-lumen-charcoal hover:bg-lumen-charcoal/90 text-white font-bold px-12 py-6 text-lg mb-4">
            Claim Your Free Audit Now
          </Button>
          <p className="text-sm text-lumen-charcoal/70">
            ✓ Usually costs $500 • ✓ Yours free for limited time • ✓ No strings attached
          </p>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              Simple 3-Step Process
            </h2>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              No complicated contracts or confusing tech talk. Here's exactly how we work together:
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {processSteps.map((step, index) => (
              <div key={index} className="text-center relative">
                <div className="w-20 h-20 bg-lumen-yellow rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold text-lumen-charcoal">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold text-lumen-charcoal mb-4">{step.title}</h3>
                <p className="text-lumen-mid-gray">{step.description}</p>
                {index < processSteps.length - 1 && (
                  <ArrowRight className="hidden md:block absolute top-10 -right-4 h-8 w-8 text-lumen-yellow" />
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features & Benefits Section */}
      <section className="py-20 bg-lumen-off-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              Everything Your Business Needs Online
            </h2>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              Professional websites built specifically for small businesses that need to compete and grow.
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-1" />
                <span className="text-lumen-charcoal text-lg">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Transparency Section */}
      <section className="py-20 bg-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              Transparent, SMO-Friendly Pricing
            </h2>
            <p className="text-xl text-lumen-mid-gray max-w-3xl mx-auto">
              No hidden fees, no surprises. Professional websites that fit your budget and deliver real ROI.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <Card className="border-2 border-lumen-yellow/20 shadow-lg">
              <CardContent className="p-8 text-center">
                <h3 className="text-xl font-bold text-lumen-charcoal mb-4">Starter Website</h3>
                <div className="text-3xl font-bold text-lumen-charcoal mb-2">$2,500</div>
                <p className="text-lumen-mid-gray mb-6">Perfect for service businesses</p>
                <ul className="text-left space-y-2 mb-8">
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />5 professional pages</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Mobile-responsive design</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Contact forms</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Basic SEO setup</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />30 days support</li>
                </ul>
                <Button className="w-full bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold">
                  Get Started
                </Button>
              </CardContent>
            </Card>

            <Card className="border-2 border-lumen-yellow shadow-xl relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-lumen-yellow text-lumen-charcoal px-4 py-2 rounded-full text-sm font-bold">Most Popular</span>
              </div>
              <CardContent className="p-8 text-center">
                <h3 className="text-xl font-bold text-lumen-charcoal mb-4">Growth Website</h3>
                <div className="text-3xl font-bold text-lumen-charcoal mb-2">$4,500</div>
                <p className="text-lumen-mid-gray mb-6">For businesses ready to scale</p>
                <ul className="text-left space-y-2 mb-8">
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />10 custom pages</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Advanced SEO optimization</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />E-commerce ready</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Analytics integration</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />90 days support</li>
                </ul>
                <Button className="w-full bg-lumen-charcoal hover:bg-lumen-charcoal/90 text-white font-bold">
                  Get Started
                </Button>
              </CardContent>
            </Card>

            <Card className="border-2 border-lumen-yellow/20 shadow-lg">
              <CardContent className="p-8 text-center">
                <h3 className="text-xl font-bold text-lumen-charcoal mb-4">Enterprise</h3>
                <div className="text-3xl font-bold text-lumen-charcoal mb-2">Custom</div>
                <p className="text-lumen-mid-gray mb-6">For complex requirements</p>
                <ul className="text-left space-y-2 mb-8">
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Unlimited pages</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Custom functionality</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Third-party integrations</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Priority support</li>
                  <li className="flex items-center"><CheckCircle className="h-4 w-4 text-lumen-yellow mr-2" />Ongoing maintenance</li>
                </ul>
                <Button variant="outline" className="w-full border-2 border-lumen-yellow text-lumen-charcoal font-bold">
                  Contact Us
                </Button>
              </CardContent>
            </Card>
          </div>
          <div className="text-center mt-12">
            <p className="text-lumen-mid-gray mb-4">💳 Payment plans available • 🔒 Money-back guarantee • 📞 Free consultation</p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-lumen-off-white">
        <div className="max-w-content mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
              Common Questions from SMO Owners
            </h2>
          </div>
          <div className="max-w-3xl mx-auto space-y-8">
            {faqs.map((faq, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <h3 className="text-xl font-bold text-lumen-charcoal mb-4">{faq.question}</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-lumen-yellow/10 to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
            Ready to Stop Losing Customers to Your Website?
          </h2>
          <p className="text-xl text-lumen-mid-gray mb-8 max-w-3xl mx-auto">
            Book your free strategy call today. We'll show you exactly what's holding your website back
            and how to fix it. No obligation, just actionable insights.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button size="lg" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-12 py-6 text-lg">
              Book Your Free Strategy Call
            </Button>
            <Button variant="outline" size="lg" className="border-2 border-lumen-charcoal/20 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg">
              Get Free Website Audit
            </Button>
          </div>
          <p className="text-sm text-lumen-mid-gray">
            ⏰ Limited spots available this month • 🎯 Usually $500 value • 💯 Zero pressure guarantee
          </p>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default WebDigital;